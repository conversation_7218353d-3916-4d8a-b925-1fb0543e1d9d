# -*- coding: utf-8 -*-
import logging
import re

logger = logging.getLogger(__name__)

# Dictionary for translations
translations = {
    'ar': {
        'welcome_trading_ai': "أهلاً بك في دورة تعلم التداول بالذكاء الاصطناعي! 📈",
        'add_gemini_key_prompt': "للبدء، يرجى إضافة مفتاح Gemini API الخاص بك. هذا سيسمح لي بتوليد محتوى تعليمي مخصص لك.",
        'add_gemini_key_button': "🔑 إضافة مفتاح Gemini API",
        'gemini_access_error': "عذرًا، لا يمكنني الوصول إلى Gemini الآن. يرجى التأكد من إضافة مفتاح API صالح.",
        'gemini_tutor_error': "حدث خطأ أثناء محاولة الإجابة على سؤالك. يرجى المحاولة مرة أخرى لاحقًا.",
        'lesson_generation_error': "عذرًا، لا يمكنني الوصول إلى Gemini الآن لتوليد الدرس. يرجى التأكد من إضافة مفتاح API صالح.",
        'chapter_button': "الفصل {number}",
        'next_chapter_button': "الفصل التالي ❯",
        'take_quiz_button': "📝 إجراء الاختبار",
        'ask_tutor_prompt': "يمكنك أيضًا طرح أي سؤال يتعلق بالتداول وسأبذل قصارى جهدي للإجابة عليه باستخدام Gemini.",
        'ask_tutor_button': '❓ اسأل مدرس الذكاء الاصطناعي',
        'ask_tutor_instruction': "يرجى كتابة سؤالك المتعلق بالتداول.",
        'quiz_already_started': "📝 يبدو أنك بدأت الاختبار بالفعل. يرجى إكمال الإجابة على الأسئلة.",
        'course_completed': "لقد أكملت الدورة الأساسية والاختبار! 🎉",
        'error_starting_course': "حدث خطأ ما. يرجى محاولة بدء الدورة مرة أخرى باستخدام /learn_trading_ai",
        'tutor_prompt_header': "أنت مدرس خبير في التداول. المستخدم يدرس حاليًا الفصل {chapter_number} حول موضوع '{chapter_topic}'. أجب على سؤاله التالي باللغة {lang} مع التركيز على المفاهيم المتعلقة بهذا الموضوع قدر الإمكان:\n\nالسؤال: {question}\n\nالإجابة:",
        'ai_conversation_system_instruction': "أنت مساعد تعليمي متخصص في تداول العملات الرقمية. المستخدم يدرس حاليًا الفصل المتعلق بـ '{chapter_topic}'. أجب على أسئلة المستخدم المتعلقة بهذا الموضوع أو بمواضيع التداول بشكل عام بلغة {lang}. حافظ على إجاباتك واضحة وموجزة ومناسبة للمبتدئين. استخدم تنسيق Markdown عند الحاجة.",
        'enhanced_tutor_prompt': "يرجى تقديم إجابة مفصلة ومتكاملة. الإجابة القصيرة جدًا غير مقبولة. يجب أن تتضمن إجابتك:\n1. شرحًا مفصلاً للمفاهيم المتعلقة بالسؤال\n2. أمثلة عملية توضح الفكرة\n3. نصائح وإرشادات للمتداولين المبتدئين\n4. معلومات إضافية مفيدة متعلقة بالموضوع\n\nيجب أن تكون الإجابة شاملة وتغطي جميع جوانب السؤال بعمق.",
        'enhanced_chapter_prompt': "يرجى تقديم محتوى تعليمي مفصل ومتكامل. المحتوى القصير جدًا غير مقبول. يجب أن يتضمن المحتوى:\n1. شرحًا مفصلاً للمفاهيم الأساسية في هذا الفصل\n2. أمثلة عملية توضح المفاهيم\n3. نصائح وإرشادات للمتداولين المبتدئين\n4. معلومات إضافية مفيدة متعلقة بالموضوع\n5. استخدام الرموز التعبيرية (الإيموجي) بشكل مناسب\n\nيجب أن يكون المحتوى شاملاً ويغطي جميع جوانب الموضوع بعمق، مع الالتزام بالتنسيق المطلوب.",

        # Supplementary Chapters Titles and Descriptions - Arabic
        'basics_chapter_title': "أساسيات التداول - مراجعة شاملة",
        'basics_chapter_desc': "مراجعة شاملة للمفاهيم الأساسية في التداول",
        'risk_management_title': "إدارة المخاطر للمبتدئين",
        'risk_management_desc': "تعلم كيفية حماية رأس مالك وتقليل الخسائر",
        'intermediate_strategies_title': "استراتيجيات التداول المتوسطة",
        'intermediate_strategies_desc': "استراتيجيات متقدمة للمتداولين ذوي الخبرة المتوسطة",
        'technical_analysis_title': "التحليل الفني المتقدم",
        'technical_analysis_desc': "فهم متعمق للمؤشرات والأنماط الفنية",
        'advanced_strategies_title': "استراتيجيات التداول المتقدمة",
        'advanced_strategies_desc': "استراتيجيات متقدمة للمتداولين المحترفين",
        'market_psychology_title': "علم نفس السوق والتداول العاطفي",
        'market_psychology_desc': "فهم العوامل النفسية التي تؤثر على قرارات التداول",
        'indicators_title': "تعمق في المؤشرات الفنية",
        'indicators_desc': "شرح مفصل للمؤشرات الفنية وكيفية استخدامها",
        'general_trading_title': "مفاهيم التداول الأساسية",
        'general_trading_desc': "مراجعة شاملة لمفاهيم التداول الأساسية",
        'chapter_gen_prompt_header': "أنت مدرس تداول خبير ومؤلف محتوى تعليمي باللغة {lang}. مهمتك هي إنشاء محتوى تعليمي للفصل رقم {chapter_number} من دورة تداول للمبتدئين مكونة من 10 فصول.",
        'guide_add_gemini_key': "يرجى استخدام الأمر /add_gemini_key لإضافة مفتاح API الخاص بك.",
        'complete_chapters_first': "يرجى إكمال جميع الفصول قبل إجراء الاختبار.",
        'quiz_starting': "🎯 بدء الاختبار!\n\nسيتم إرسال الأسئلة واحداً تلو الآخر. لديك 30 ثانية للإجابة على كل سؤال.",
        'generating_quiz': "⏳ جاري إنشاء الاختبار...",
        'quiz_results': "نتائج الاختبار",
        'correct_answers': "الإجابات الصحيحة",
        'percentage': "النسبة المئوية",
        'excellent_result': "🌟 ممتاز! لديك فهم ممتاز للمفاهيم.",
        'good_result': "👍 جيد! لديك فهم جيد للمفاهيم الأساسية.",
        'average_result': "🤔 متوسط. هناك بعض المفاهيم التي تحتاج مراجعة.",
        'needs_improvement': "📚 تحتاج إلى مزيد من الدراسة. لا تقلق، استمر في التعلم!",
        'results_error': "حدث خطأ أثناء عرض النتائج. يرجى المحاولة مرة أخرى.",
        'ask_again_prompt': "يمكنك طرح سؤال آخر أو الانتقال إلى الفصل التالي.",
        'operation_in_progress': "⏳ جاري إنشاء الفصل... يرجى الانتظار.",
        'quiz_generation_in_progress': "⏳ جاري إنشاء الاختبار... يرجى الانتظار.",
        'continue_or_ask': "يمكنك الآن المتابعة في الفصل أو طرح سؤال آخر.",
        'review_material_title': "مواد للمراجعة",
        'supplementary_chapters': "📚 فصول تكميلية مخصصة",
        'back_to_main': "🏠 العودة إلى القائمة الرئيسية",
        'next_steps': "ماذا تريد أن تفعل الآن؟",
        'generating_chapter': "⏳ جاري إنشاء الفصل التكميلي...",
        'chapter_not_found': "الفصل المطلوب غير موجود.",
        'chapter_generation_error': "حدث خطأ أثناء إنشاء الفصل التكميلي. يرجى المحاولة مرة أخرى لاحقًا.",
        'back_to_chapters': "🔙 العودة إلى قائمة الفصول التكميلية",
        'no_supplementary_chapters': "لا توجد فصول تكميلية متاحة حاليًا. يرجى المحاولة مرة أخرى لاحقًا.",
        'supplementary_chapters_list': "📚 الفصول التكميلية المخصصة لك بناءً على نتيجة الاختبار:",
        'back_button': "🔙 رجوع",
        'review_generation_error': "حدث خطأ أثناء إنشاء مواد المراجعة. يرجى المحاولة مرة أخرى لاحقًا.",
        # الشروط والأحكام
        'terms_and_conditions_title': "📜 الشروط والأحكام",
        'terms_and_conditions_content': "\u26a0 إخلاء المسؤولية القانوني\n\n1. هذا البوت يقدم تحليلات فنية فقط وليست نصائح استثمارية\n2. أنت المسؤول الوحيد عن قراراتك الاستثمارية\n3. لا نتحمل مسؤولية أي خسائر مالية\n4. استخدام البوت يعني موافقتك على الشروط\n5. عدم إعطاء مفاتيح الوصول بدقة قد يؤدي لفقدان اشتراكك\n6. لا يوجد دعم فني - اتبع التعليمات بدقة\n7. المؤشرات الفنية لا يمكن أن تكون 100% دقيقة وتعتمد على بيانات تاريخية\n8. الأسواق المالية متقلبة تنطوي على مخاطر عالية\n9. لا نضمن أي أرباح أو عوائد مالية\n10. البوت قد يتعطل في أي وقت دون إشعار مسبق\n11. نحن بريئون تماما من أي استخدام للبوت في تداول العملات\n12. أي قرارات تداول تتخذها بناء على تحليلات البوت هي مسؤوليتك الكاملة\n13. مطور البوت غير مسؤول عن أي خسائر مالية قد تنتج من استخدام البوت\n14. استخدام البوت للتداول يتم على مسؤوليتك الشخصية الكاملة\n\n🔒 شروط الاستخدام:\n\u2022 البوت للتحليل الفني فقط\n\u2022 لا نضمن دقة التحليلات\n\u2022 لا نقدم استشارات مالية\n\u2022 الاشتراك غير قابل للاسترداد\n\u2022 استخدام البوت لأغراض غير مشروعة\n\u2022 يحظر مشاركة حساب الاشتراك مع الآخرين\n\u2022 نحتفظ بحق إيقاف أي حساب يخالف الشروط\n\u2022 قد نقوم بتحديث الشروط في أي وقت دون إشعار\n\u2022 البيانات المقدمة قد تكون متأخرة عن السوق الفعلي\n\u2022 لا نتحمل مسؤولية أي أعطال فنية أو انقطاع في الخدمة\n\n\u26a0 تحذيرات إضافية:\n\u2022 تداول العملات الرقمية ينطوي على مخاطر عالية\n\u2022 لا تستثمر أكثر مما يمكنك تحمل خسارته\n\u2022 قم بإجراء بحثك الخاص قبل اتخاذ أي قرار\n\u2022 كن حذرا من عمليات الاحتيال والمشاريع الوهمية\n\u2022 تأكد من فهم آلية عمل المؤشرات الفنية قبل استخدامها",
        'terms_agree_button': "✅ أوافق على الشروط والأحكام",
        'terms_decline_button': "❌ لا أوافق",
        'terms_agree_success': "شكراً لموافقتك على الشروط والأحكام",
        'terms_decline_message': "للأسف، يجب الموافقة على الشروط والأحكام لاستخدام البوت",
        'terms_required_message': "للأسف، يجب الموافقة على الشروط والأحكام لاستخدام البوت. يمكنك إعادة تشغيل البوت في أي وقت للموافقة على الشروط. 🔄",
        'language_selected': "تم اختيار اللغة بنجاح",
        'error_saving_settings': "حدث خطأ أثناء حفظ الإعدادات. الرجاء المحاولة مرة أخرى",

        # AI Learning Content Generation Prompts - Arabic
        'chapter_generation_intro': "أنت خبير في تعليم التداول بالعملات الرقمية. اشرح الموضوع التالي للمبتدئين:",
        'chapter_generation_topic': "**الموضوع:** {topic}",
        'chapter_generation_requirements': "**المطلوب:**",
        'chapter_generation_req1': "1. **شرح مفصل وواضح:** استخدم لغة بسيطة ومباشرة.",
        'chapter_generation_req2': "2. **أمثلة عملية:** قدم أمثلة لتوضيح المفاهيم.",
        'chapter_generation_req3': "3. **تنسيق Telegram:** استخدم تنسيق Telegram Markdown البسيط (عناوين بـ **, قوائم نقطية، خط عريض، إلخ). لا تستخدم ```markdown أو أي code blocks.",
        'chapter_generation_req4': "4. **استخدام الإيموجيات بكثرة:** أضف إيموجيات مناسبة للمحتوى في كل فقرة وعنوان لجعله أكثر جاذبية وسهولة في القراءة. استخدم مجموعة متنوعة من الإيموجيات المناسبة للتداول مثل (📈, 📉, 📊, 💡, 💰, 🤔, 📱, 💹, 📋, 🔍, 🎯, ⚠️, ✅, ❌, 💼, 🔄, 📆, 🔔, 📢, 💪, 🧠, 🔑, 🛡️, 🚀, 💎, 🔮). ضع إيموجي مناسب قبل كل عنوان وفي بداية كل فقرة مهمة.",
        'chapter_generation_req5': "5. **التركيز على المبتدئين:** تجنب المصطلحات المعقدة قدر الإمكان أو اشرحها ببساطة.",
        'chapter_generation_req6': "6. **الرد باللغة العربية فقط:** لا تستخدم أي لغة أخرى في المحتوى.",
        'chapter_generation_important': "**هام:** لا تستخدم أي تنسيق خارج Telegram Markdown البسيط. تجنب استخدام الجداول أو الروابط أو code blocks. اجعل المحتوى مناسباً لعرضه في رسالة Telegram واحدة (أقل من 3500 حرف).",

        'quiz_generation_intro': "أنت مدرس خبير في التداول ومصمم اختبارات محترف. مهمتك هي إنشاء {num_questions} أسئلة اختبار من نوع الاختيار من متعدد باللغة {lang} لتقييم فهم المتعلم للمفاهيم التالية:",
        'quiz_generation_requirements': "**متطلبات الأسئلة:**",
        'quiz_generation_req1': "1. يجب أن تكون الأسئلة متنوعة وتغطي مفاهيم مختلفة من الفصول المذكورة أعلاه.",
        'quiz_generation_req2': "2. كل سؤال يجب أن يكون له 4 خيارات بالضبط، مع إجابة صحيحة واحدة فقط.",
        'quiz_generation_req3': "3. يجب أن تكون الأسئلة واضحة ومباشرة ومناسبة لمستوى المبتدئين.",
        'quiz_generation_req4': "4. يجب أن تكون الخيارات واقعية ومعقولة (لا تضع خيارات سخيفة أو غير منطقية).",
        'quiz_generation_format': "**تنسيق الإخراج المطلوب:**",
        'quiz_generation_format_note': "ملاحظة: تأكد من أن correct_option_id هو رقم صحيح يمثل فهرس الخيار الصحيح في مصفوفة options (0 للخيار الأول، 1 للخيار الثاني، إلخ).",
        'quiz_generation_json_only': "أنتج JSON صالح فقط بدون أي نص إضافي أو شرح.",

        'review_material_intro': "أنت مدرس خبير في التداول. المستخدم يحتاج إلى مراجعة المفاهيم التالية التي واجه صعوبة فيها خلال الاختبار:",
        'review_material_instruction': "قم بإنشاء ملخص مراجعة موجز وواضح باللغة {lang} يغطي هذه المفاهيم. استخدم تنسيق نصي بسيط (بدون Markdown معقد) واستخدم الإيموجي المناسبة لجعل المحتوى أكثر جاذبية.",
        'review_material_should_include': "يجب أن يتضمن الملخص:",
        'review_material_point1': "1. شرح مبسط للمفاهيم الأساسية",
        'review_material_point2': "2. نقاط رئيسية يجب تذكرها",
        'review_material_point3': "3. أمثلة توضيحية قصيرة",
        'review_material_point4': "4. نصائح عملية للتطبيق",
        'review_material_concise': "اجعل المحتوى موجزًا ومركزًا ومفيدًا للمراجعة السريعة.",
        'review_material_formatting_notes': "ملاحظات مهمة للتنسيق:",
        'review_material_format1': "- تجنب استخدام علامات Markdown المعقدة",
        'review_material_format2': "- استخدم النجمة (*) للنقاط فقط",
        'review_material_format3': "- تجنب استخدام الروابط",
        'review_material_format4': "- تجنب استخدام الجداول",
        'review_material_format5': "- تجنب استخدام الرموز الخاصة مثل _ و ~ و ` و | و > بكثرة",
        'review_material_format6': "- تجنب استخدام علامات النجمة المزدوجة (**) للتنسيق",
        'review_material_format7': "- استخدم الإيموجي بشكل معتدل",

        'supplementary_chapter_intro': "أنت مدرس خبير في التداول ومؤلف محتوى تعليمي. مهمتك هي إنشاء فصل تكميلي مخصص للمتعلم باللغة {lang}.",
        'supplementary_chapter_info': "**معلومات الفصل:**",
        'supplementary_chapter_title': "- العنوان: {title}",
        'supplementary_chapter_description': "- الوصف: {description}",
        'supplementary_chapter_level': "- المستوى: {level}",
        'supplementary_chapter_topic': "- الموضوع: {topic}",
        'supplementary_chapter_content_instructions': "**تعليمات المحتوى:**",
        'supplementary_chapter_inst1': "1. قم بإنشاء محتوى تعليمي موجز وفعال حول الموضوع المحدد.",
        'supplementary_chapter_inst2': "2. استخدم لغة واضحة ومباشرة تناسب مستوى المتعلم ({level}).",
        'supplementary_chapter_inst3': "3. قسّم المحتوى إلى أقسام منطقية مع عناوين فرعية.",
        'supplementary_chapter_inst4': "4. أضف أمثلة عملية موجزة لتوضيح المفاهيم.",
        'supplementary_chapter_inst5': "5. استخدم الإيموجي المناسبة لجعل المحتوى أكثر جاذبية.",
        'supplementary_chapter_inst6': "6. اختم بملخص قصير للنقاط الرئيسية.",
        'supplementary_chapter_format': "**تنسيق المحتوى:**",
        'supplementary_chapter_format1': "- استخدم تنسيق نصي بسيط (تجنب Markdown المعقد).",
        'supplementary_chapter_format2': "- استخدم الإيموجي بشكل مناسب في بداية كل قسم.",
        'supplementary_chapter_format3': "- تجنب استخدام الرموز الخاصة مثل _ و ~ و ` و | و > بكثرة.",
        'supplementary_chapter_format4': "- تجنب استخدام الروابط والجداول.",
        'supplementary_chapter_format5': "- تجنب استخدام علامات النجمة المزدوجة (**) للتنسيق.",
        'supplementary_chapter_format6': "- يجب أن يكون المحتوى موجزًا ولا يتجاوز 3000 حرف.",
        'supplementary_chapter_conclusion': "أنشئ محتوى تعليميًا موجزًا وفعالًا يساعد المتعلم على فهم الموضوع بشكل أفضل.",
        # Add other keys from trading_education.py as needed
    },
    'en': {
        'welcome_trading_ai': "Welcome to the AI Trading Learning Course! 📈",
        'add_gemini_key_prompt': "To get started, please add your Gemini API key. This will allow me to generate personalized educational content for you.",
        'add_gemini_key_button': "🔑 Add Gemini API Key",
        'gemini_access_error': "Sorry, I can't access Gemini right now. Please ensure you have added a valid API key.",
        'gemini_tutor_error': "An error occurred while trying to answer your question. Please try again later.",
        'lesson_generation_error': "Sorry, I can't access Gemini right now to generate the lesson. Please ensure you have added a valid API key.",
        'chapter_button': "Chapter {number}",
        'next_chapter_button': "Next Chapter ❯",
        'take_quiz_button': "📝 Take the Quiz",
        'ask_tutor_prompt': "You can also ask any trading-related question, and I'll do my best to answer it using Gemini.",
        'ask_tutor_button': '❓ Ask AI Tutor',
        'ask_tutor_instruction': "Please type your trading question.",
        'quiz_already_started': "📝 It looks like you've already started the quiz. Please complete answering the questions.",
        'course_completed': "You have completed the basic course and the quiz! 🎉",
        'error_starting_course': "Something went wrong. Please try starting the course again using /learn_trading_ai",
        'tutor_prompt_header': "You are an expert trading tutor. The user is currently studying chapter {chapter_number} about '{chapter_topic}'. Answer their following question in {lang} with focus on concepts related to this topic as much as possible:\n\nQuestion: {question}\n\nAnswer:",
        'ai_conversation_system_instruction': "You are an educational assistant specialized in cryptocurrency trading. The user is currently studying the chapter related to '{chapter_topic}'. Answer the user's questions related to this topic or trading topics in general in {lang}. Keep your answers clear, concise, and appropriate for beginners. Use Markdown formatting when needed.",
        'enhanced_tutor_prompt': "Please provide a detailed and comprehensive answer. Very short responses are not acceptable. Your answer must include:\n1. Detailed explanation of concepts related to the question\n2. Practical examples illustrating the idea\n3. Tips and guidance for beginner traders\n4. Additional useful information related to the topic\n\nThe answer should be comprehensive and cover all aspects of the question in depth.",
        'enhanced_chapter_prompt': "Please provide detailed and comprehensive educational content. Very short content is not acceptable. Your content must include:\n1. Detailed explanation of the core concepts in this chapter\n2. Practical examples illustrating the concepts\n3. Tips and guidance for beginner traders\n4. Additional useful information related to the topic\n5. Appropriate use of emojis\n\nThe content should be comprehensive and cover all aspects of the topic in depth, while following the requested format.",

        # Supplementary Chapters Titles and Descriptions - English
        'basics_chapter_title': "Trading Basics - Comprehensive Review",
        'basics_chapter_desc': "Comprehensive review of fundamental trading concepts",
        'risk_management_title': "Risk Management for Beginners",
        'risk_management_desc': "Learn how to protect your capital and minimize losses",
        'intermediate_strategies_title': "Intermediate Trading Strategies",
        'intermediate_strategies_desc': "Advanced strategies for traders with intermediate experience",
        'technical_analysis_title': "Advanced Technical Analysis",
        'technical_analysis_desc': "Deep understanding of technical indicators and patterns",
        'advanced_strategies_title': "Advanced Trading Strategies",
        'advanced_strategies_desc': "Advanced strategies for professional traders",
        'market_psychology_title': "Market Psychology and Emotional Trading",
        'market_psychology_desc': "Understanding psychological factors that affect trading decisions",
        'indicators_title': "Deep Dive into Technical Indicators",
        'indicators_desc': "Detailed explanation of technical indicators and how to use them",
        'general_trading_title': "Basic Trading Concepts",
        'general_trading_desc': "Comprehensive review of basic trading concepts",

        # AI Learning Content Generation Prompts
        'chapter_generation_intro': "You are an expert in cryptocurrency trading education. Explain the following topic for beginners:",
        'chapter_generation_topic': "**Topic:** {topic}",
        'chapter_generation_requirements': "**Requirements:**",
        'chapter_generation_req1': "1. **Detailed and clear explanation:** Use simple and direct language.",
        'chapter_generation_req2': "2. **Practical examples:** Provide examples to illustrate concepts.",
        'chapter_generation_req3': "3. **Telegram formatting:** Use simple Telegram Markdown formatting (headings with **, bullet points, bold text, etc). Do not use ```markdown or any code blocks.",
        'chapter_generation_req4': "4. **Use emojis extensively:** Add appropriate emojis to content in each paragraph and heading to make it more attractive and easy to read. Use a variety of trading-appropriate emojis such as (📈, 📉, 📊, 💡, 💰, 🤔, 📱, 💹, 📋, 🔍, 🎯, ⚠️, ✅, ❌, 💼, 🔄, 📆, 🔔, 📢, 💪, 🧠, 🔑, 🛡️, 🚀, 💎, 🔮). Place an appropriate emoji before each heading and at the beginning of each important paragraph.",
        'chapter_generation_req5': "5. **Focus on beginners:** Avoid complex terminology as much as possible or explain it simply.",
        'chapter_generation_req6': "6. **Respond in English only:** Do not use any other language in the content.",
        'chapter_generation_important': "**Important:** Do not use any formatting outside simple Telegram Markdown. Avoid using tables, links, or code blocks. Make content suitable for display in a single Telegram message (less than 3500 characters).",

        'quiz_generation_intro': "You are an expert trading teacher and professional quiz designer. Your task is to create {num_questions} multiple-choice quiz questions in {lang} to assess the learner's understanding of the following concepts:",
        'quiz_generation_requirements': "**Question Requirements:**",
        'quiz_generation_req1': "1. Questions should be diverse and cover different concepts from the chapters mentioned above.",
        'quiz_generation_req2': "2. Each question must have exactly 4 options, with only one correct answer.",
        'quiz_generation_req3': "3. Questions should be clear, direct, and appropriate for beginner level.",
        'quiz_generation_req4': "4. Options should be realistic and reasonable (don't include silly or illogical options).",
        'quiz_generation_format': "**Required Output Format:**",
        'quiz_generation_format_note': "Note: Make sure correct_option_id is an integer representing the index of the correct option in the options array (0 for first option, 1 for second option, etc).",
        'quiz_generation_json_only': "Produce valid JSON only without any additional text or explanation.",

        'review_material_intro': "You are an expert trading teacher. The user needs to review the following concepts that they had difficulty with during the quiz:",
        'review_material_instruction': "Create a brief and clear review summary in {lang} covering these concepts. Use simple text formatting (without complex Markdown) and use appropriate emojis to make the content more attractive.",
        'review_material_should_include': "The summary should include:",
        'review_material_point1': "1. Simplified explanation of basic concepts",
        'review_material_point2': "2. Key points to remember",
        'review_material_point3': "3. Short illustrative examples",
        'review_material_point4': "4. Practical tips for application",
        'review_material_concise': "Make the content concise, focused, and useful for quick review.",
        'review_material_formatting_notes': "Important formatting notes:",
        'review_material_format1': "- Avoid using complex Markdown tags",
        'review_material_format2': "- Use asterisk (*) for bullet points only",
        'review_material_format3': "- Avoid using links",
        'review_material_format4': "- Avoid using tables",
        'review_material_format5': "- Avoid using special characters like _ and ~ and ` and | and > extensively",
        'review_material_format6': "- Avoid using double asterisks (**) for formatting",
        'review_material_format7': "- Use emojis moderately",

        'supplementary_chapter_intro': "You are an expert trading teacher and educational content author. Your task is to create a customized supplementary chapter for the learner in {lang}.",
        'supplementary_chapter_info': "**Chapter Information:**",
        'supplementary_chapter_title': "- Title: {title}",
        'supplementary_chapter_description': "- Description: {description}",
        'supplementary_chapter_level': "- Level: {level}",
        'supplementary_chapter_topic': "- Topic: {topic}",
        'supplementary_chapter_content_instructions': "**Content Instructions:**",
        'supplementary_chapter_inst1': "1. Create concise and effective educational content on the specified topic.",
        'supplementary_chapter_inst2': "2. Use clear and direct language appropriate for the learner's level ({level}).",
        'supplementary_chapter_inst3': "3. Divide content into logical sections with subheadings.",
        'supplementary_chapter_inst4': "4. Add brief practical examples to illustrate concepts.",
        'supplementary_chapter_inst5': "5. Use appropriate emojis to make content more attractive.",
        'supplementary_chapter_inst6': "6. End with a brief summary of key points.",
        'supplementary_chapter_format': "**Content Format:**",
        'supplementary_chapter_format1': "- Use simple text formatting (avoid complex Markdown).",
        'supplementary_chapter_format2': "- Use emojis appropriately at the beginning of each section.",
        'supplementary_chapter_format3': "- Avoid using special characters like _ and ~ and ` and | and > extensively.",
        'supplementary_chapter_format4': "- Avoid using links and tables.",
        'supplementary_chapter_format5': "- Avoid using double asterisks (**) for formatting.",
        'supplementary_chapter_format6': "- Content should be concise and not exceed 3000 characters.",
        'supplementary_chapter_conclusion': "Create concise and effective educational content that helps the learner understand the topic better.",
        'chapter_gen_prompt_header': "You are an expert trading tutor and educational content creator in {lang}. Your task is to create educational content for chapter {chapter_number} of a 10-chapter beginner trading course.",
        'guide_add_gemini_key': "Please use the /add_gemini_key command to add your API key.",
        'complete_chapters_first': "Please complete all chapters before taking the quiz.",
        'quiz_starting': "🎯 Starting the quiz!\n\nQuestions will be sent one by one. You have 30 seconds to answer each question.",
        'generating_quiz': "⏳ Generating quiz...",
        'quiz_results': "Quiz Results",
        'correct_answers': "Correct Answers",
        'percentage': "Percentage",
        'excellent_result': "🌟 Excellent! You have an excellent understanding of the concepts.",
        'good_result': "👍 Good! You have a good understanding of the basic concepts.",
        'average_result': "🤔 Average. There are some concepts that need review.",
        'needs_improvement': "📚 You need more study. Don't worry, keep learning!",
        'results_error': "An error occurred while displaying results. Please try again.",
        'ask_again_prompt': "You can ask another question or proceed to the next chapter.",
        'operation_in_progress': "⏳ Generating chapter... Please wait.",
        'quiz_generation_in_progress': "⏳ Generating quiz... Please wait.",
        'continue_or_ask': "You can now continue with the chapter or ask another question.",
        'review_material_title': "Review Materials",
        'supplementary_chapters': "📚 Customized Supplementary Chapters",
        'back_to_main': "🏠 Back to Main Menu",
        'next_steps': "What would you like to do now?",
        'generating_chapter': "⏳ Generating supplementary chapter...",
        'chapter_not_found': "The requested chapter was not found.",
        'chapter_generation_error': "An error occurred while generating the supplementary chapter. Please try again later.",
        'back_to_chapters': "🔙 Back to Chapters List",
        'no_supplementary_chapters': "No supplementary chapters are available at this time. Please try again later.",
        'supplementary_chapters_list': "📚 Customized supplementary chapters based on your quiz results:",
        'back_button': "🔙 Back",
        'review_generation_error': "An error occurred while generating review materials. Please try again later.",
        # Terms and Conditions
        'terms_and_conditions_title': "📜 Terms and Conditions",
        'terms_and_conditions_content': "**Terms and Conditions for Using the Trading Analysis Bot**\n\n1. **General Use**: This bot is designed to provide cryptocurrency trading analysis and recommendations for informational purposes only.\n\n2. **Disclaimer**: The information provided is not financial advice, and we are not responsible for any losses that may result from using this information.\n\n3. **Subscription**: Some features are only available to subscribed users. Subscriptions are automatically renewed unless canceled.\n\n4. **Privacy**: We collect limited data to improve your experience. We will not share your personal information with third parties without your consent.\n\n5. **API**: When adding your API keys, you agree that we use them only to access your account data to provide the requested services.\n\n6. **Changes**: We reserve the right to modify these terms at any time. Users will be notified of significant changes.",
        'agree_button': "✅ I Agree to Terms",
        'disagree_button': "❌ I Disagree",
        'terms_accepted': "Thank you for accepting the terms and conditions",
        'terms_declined': "Unfortunately, you must agree to the terms to use the bot",
        'terms_required': "Unfortunately, you must agree to the terms to use the bot. You can restart the bot at any time to agree to the terms. 🔄",
        'language_selected': "Language selected successfully",
        'error_saving_settings': "An error occurred while saving settings. Please try again",
        # Add other keys from trading_education.py as needed
    }
}

def get_text(key: str, lang: str = 'ar', default: str = None, **kwargs) -> str:
    """Gets translated text, falling back to Arabic, then default, then the key itself."""
    try:
        # Try the requested language
        text = translations.get(lang, {}).get(key)
        # If not found, try Arabic
        if text is None:
            text = translations.get('ar', {}).get(key)
        # If still not found, use the provided default or the key itself
        if text is None:
            text = default if default is not None else f"[{key}]"
            logger.warning(f"Translation key '{key}' not found for lang '{lang}' or 'ar'. Used default/key.")

        return text.format(**kwargs)
    except KeyError as e:
        logger.error(f"Missing format key {e} for translation key '{key}' in lang '{lang}'")
        return default if default is not None else f"[{key} - FORMAT ERROR]"
    except Exception as e:
        logger.error(f"Error in get_text for key '{key}', lang '{lang}': {e}")
        return default if default is not None else f"[{key} - ERROR]"

def fix_bold_formatting(text: str) -> str:
    """
    إصلاح تنسيق النص العريض في تلغرام وإزالة مشاكل Markdown

    يقوم بإزالة النقطتين من داخل النص العريض ووضعها خارجه
    مثال: **عنوان:** -> **عنوان**:
    كما يقوم بإزالة أي code blocks غير مرغوب فيها

    Args:
        text: النص المراد إصلاحه

    Returns:
        النص بعد إصلاح التنسيق
    """
    if not text:
        return text

    # إزالة أي بادئات أو لواحق غير متوقعة قد تسبب مشاكل في التحليل (مثل ```markdown)
    text = text.strip()
    if text.startswith("```markdown"):
        text = text[len("```markdown"):].strip()
    if text.startswith("```"):
        text = text[3:].strip()
    if text.endswith("```"):
        text = text[:-3].strip()

    # إزالة النقطتين من داخل النص العريض
    text = re.sub(r'\*\*(.*?):\*\*', r'**\1**:', text)

    # إزالة النقطتين المكررة في حالة وجودها
    text = text.replace('**:', '**:')

    # استبدال أي عنوان يحتوي على نقطتين داخل النص العريض
    text = re.sub(r'\*\*(.*?):\*\*', r'**\1**:', text)

    return text

def clean_markdown_content(text: str) -> str:
    """
    تنظيف محتوى Markdown لجعله مناسباً لـ Telegram

    Args:
        text: النص المراد تنظيفه

    Returns:
        النص بعد التنظيف
    """
    if not text:
        return text

    # إزالة أي code blocks غير مرغوب فيها
    text = fix_bold_formatting(text)

    # إزالة أي تنسيق معقد غير مدعوم في Telegram
    # إزالة الجداول البسيطة
    text = re.sub(r'\|.*?\|', '', text)

    # إزالة الروابط المعقدة وترك النص فقط
    text = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', text)

    # تنظيف الأسطر الفارغة المتكررة
    text = re.sub(r'\n\n\n+', '\n\n', text)

    # إزالة المسافات الزائدة في بداية ونهاية النص
    text = text.strip()

    return text